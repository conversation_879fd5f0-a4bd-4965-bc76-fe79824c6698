#!/usr/bin/env python3
"""
Test script for the iterative improvement agent.
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

from iterative_improvement_agent import IterativeImprovementAgent


async def test_agent_creation():
    """Test that the agent can be created successfully."""
    try:
        agent = IterativeImprovementAgent()
        print("✅ Agent created successfully")
        print(f"   Host: {agent.embucket_host}")
        print(f"   Port: {agent.embucket_port}")
        print(f"   Max iterations: {agent.max_iterations}")
        print(f"   Queries per iteration: {agent.queries_per_iteration}")
        return True
    except Exception as e:
        print(f"❌ Failed to create agent: {e}")
        return False


async def test_tools_import():
    """Test that all tools can be imported and are available."""
    try:
        from tools.comprehensive_fuzzing_tool import run_comprehensive_fuzzing_session
        from tools.query_generator_improvement_tool import analyze_and_improve_query_generator
        from tools.code_modification_tool import (
            apply_query_generator_improvements, 
            backup_and_restore_query_generator,
            read_query_generator_code
        )
        print("✅ All tools imported successfully")
        return True
    except Exception as e:
        print(f"❌ Failed to import tools: {e}")
        return False


async def test_read_query_generator():
    """Test reading the current query generator code."""
    try:
        # Test reading the query generator code directly
        import os
        target_file = "tools/random_query_generator_tool.py"

        if not os.path.exists(target_file):
            print(f"❌ Query generator file not found: {target_file}")
            return False

        with open(target_file, 'r') as f:
            code = f.read()

        if "class RandomQueryGenerator" in code:
            print("✅ Query generator code read successfully")
            print(f"   Code length: {len(code)} characters")
            return True
        else:
            print("❌ Query generator code doesn't contain expected class")
            return False

    except Exception as e:
        print(f"❌ Failed to test read query generator: {e}")
        return False


async def test_backup_functionality():
    """Test the backup functionality."""
    try:
        import os
        import time

        # Test creating a backup manually (simulating the tool behavior)
        target_file = "tools/random_query_generator_tool.py"

        if not os.path.exists(target_file):
            print(f"❌ Target file not found: {target_file}")
            return False

        # Create a test backup
        timestamp = int(time.time())
        backup_path = f"{target_file}.backup.{timestamp}"

        with open(target_file, 'r') as source:
            content = source.read()

        with open(backup_path, 'w') as backup:
            backup.write(content)

        # Verify backup was created
        if os.path.exists(backup_path):
            print("✅ Backup functionality works")
            print(f"   Backup path: {backup_path}")
            # Clean up test backup
            os.remove(backup_path)
            return True
        else:
            print("❌ Backup file was not created")
            return False

    except Exception as e:
        print(f"❌ Failed to test backup: {e}")
        return False


async def main():
    """Run all tests."""
    print("🧪 Testing Iterative Improvement Agent")
    print("=" * 50)
    
    tests = [
        ("Agent Creation", test_agent_creation),
        ("Tools Import", test_tools_import),
        ("Read Query Generator", test_read_query_generator),
        ("Backup Functionality", test_backup_functionality),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 Testing: {test_name}")
        try:
            if await test_func():
                passed += 1
            else:
                print(f"   Test failed")
        except Exception as e:
            print(f"   Test error: {e}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The iterative improvement agent is ready to use.")
        print("\nTo run the agent:")
        print("  python iterative_improvement_agent.py")
    else:
        print("❌ Some tests failed. Please check the configuration and dependencies.")
        return 1
    
    return 0


if __name__ == "__main__":
    asyncio.run(main())
