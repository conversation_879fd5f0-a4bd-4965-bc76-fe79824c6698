#!/usr/bin/env python3
"""
Test script to simulate agent file access and debug any issues.
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

from agents import Agent, Runner
from tools.code_modification_tool import read_query_generator_code


class TestAgent(Agent):
    """Simple test agent to check file access."""

    def __init__(self):
        super().__init__(
            name="TestAgent",
            instructions="You are a test agent. Use read_query_generator_code to read the query generator file and report the result.",
            tools=[read_query_generator_code]
        )


async def test_agent_file_access():
    """Test if the agent can access the query generator file."""
    try:
        print("🧪 Testing Agent File Access")
        print("=" * 50)
        
        # Test direct function call first
        print("1. Testing direct function call...")
        print(f"   Current working directory: {os.getcwd()}")
        
        # Import the actual function (not the tool wrapper)
        from tools.code_modification_tool import _find_query_generator_file
        file_path = _find_query_generator_file()
        print(f"   Found file at: {file_path}")
        
        if file_path and os.path.exists(file_path):
            with open(file_path, 'r') as f:
                content = f.read()
            print(f"   ✅ Direct access successful, file size: {len(content)} characters")
        else:
            print(f"   ❌ Direct access failed")
            return False
        
        # Test through agent
        print("\n2. Testing through agent...")
        agent = TestAgent()
        
        instruction = """
        You must use the read_query_generator_code tool to read the query generator file.
        Call the tool and report exactly what it returns.
        If it returns an error, report the exact error message.
        If it succeeds, report the first 100 characters of the code.
        """
        
        result = await Runner.run(agent, instruction, max_turns=3)
        print(f"   Agent result: {result.final_output}")
        
        # Check if the result indicates success
        if "Error:" in result.final_output:
            print("   ❌ Agent access failed")
            return False
        else:
            print("   ✅ Agent access successful")
            return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run the test."""
    success = await test_agent_file_access()
    
    if success:
        print("\n🎉 All tests passed! The agent can access the query generator file.")
    else:
        print("\n❌ Tests failed. There may be an issue with file access through the agent.")
    
    return 0 if success else 1


if __name__ == "__main__":
    asyncio.run(main())
