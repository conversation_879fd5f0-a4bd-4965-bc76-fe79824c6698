#!/usr/bin/env python3
"""
Test script to verify file detection works correctly.
"""

import os
import sys

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_file_detection():
    """Test the file detection function directly."""
    print("Testing file detection...")
    print(f"Current working directory: {os.getcwd()}")
    
    # Import the function
    from tools.code_modification_tool import _find_query_generator_file
    
    # Test file detection
    result = _find_query_generator_file()
    
    if result:
        print(f"✅ SUCCESS: Found file at {result}")
        print(f"File exists: {os.path.exists(result)}")
        
        # Test reading the file
        try:
            with open(result, 'r') as f:
                content = f.read()
            print(f"✅ File readable, length: {len(content)} characters")
            return True
        except Exception as e:
            print(f"❌ Error reading file: {e}")
            return False
    else:
        print("❌ FAILED: File not found")
        return False

def test_tool_function():
    """Test the actual tool function."""
    print("\nTesting read_query_generator_code function...")
    
    # Import the function (it's decorated, so we need to access the underlying function)
    from tools.code_modification_tool import read_query_generator_code
    
    # The function is decorated with @function_tool, so we need to call it differently
    # Let's test by calling the underlying implementation
    try:
        from tools.code_modification_tool import _find_query_generator_file
        
        target_file_path = _find_query_generator_file()
        if not target_file_path:
            print("❌ Could not find file")
            return False
            
        if not os.path.exists(target_file_path):
            print(f"❌ File does not exist: {target_file_path}")
            return False
            
        with open(target_file_path, 'r') as f:
            content = f.read()
            
        print(f"✅ SUCCESS: Read {len(content)} characters from query generator")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("FILE DETECTION TEST")
    print("=" * 60)
    
    success1 = test_file_detection()
    success2 = test_tool_function()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("✅ ALL TESTS PASSED")
        sys.exit(0)
    else:
        print("❌ SOME TESTS FAILED")
        sys.exit(1)
