#!/usr/bin/env python3
"""
Comprehensive test for all code modification tools.
"""

import os
import sys
import asyncio
import json
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

from agents import Agent, Runner
from tools.code_modification_tool import (
    read_query_generator_code,
    apply_query_generator_improvements,
    backup_and_restore_query_generator
)
from tools.query_generator_improvement_tool import analyze_and_improve_query_generator


class TestAllToolsAgent(Agent):
    """Test agent for all code modification tools."""

    def __init__(self):
        super().__init__(
            name="TestAllToolsAgent",
            instructions="You are a test agent. Test each tool and report the results clearly.",
            tools=[
                read_query_generator_code,
                apply_query_generator_improvements,
                backup_and_restore_query_generator,
                analyze_and_improve_query_generator
            ]
        )


async def test_all_tools():
    """Test all tools through the agent."""
    try:
        print("🧪 Testing All Code Modification Tools")
        print("=" * 60)
        
        agent = TestAllToolsAgent()
        
        # Test 1: Read query generator code
        print("\n1. Testing read_query_generator_code...")
        read_instruction = """
        Use read_query_generator_code to read the current query generator file.
        Report if it was successful and the first 50 characters of the code.
        """
        
        read_result = await Runner.run(agent, read_instruction, max_turns=3)
        print(f"   Result: {read_result.final_output[:200]}...")
        
        # Test 2: Create a backup
        print("\n2. Testing backup functionality...")
        backup_instruction = """
        Use backup_and_restore_query_generator with action="backup" to create a backup.
        Report the result including the backup path if successful.
        """
        
        backup_result = await Runner.run(agent, backup_instruction, max_turns=3)
        print(f"   Result: {backup_result.final_output[:200]}...")
        
        # Test 3: Test improvement analysis (mock data)
        print("\n3. Testing improvement analysis...")
        mock_fuzzing_results = json.dumps({
            "execution_summary": {
                "queries_executed": 5,
                "successful_queries": 4,
                "expected_errors": 1,
                "actual_bugs": 0
            },
            "detailed_logs": []
        })
        
        analysis_instruction = f"""
        Use analyze_and_improve_query_generator with these parameters:
        - fuzzing_results: {mock_fuzzing_results}
        - current_generator_code: "mock code"
        - iteration_number: 1
        
        Report if the analysis was successful and what improvements were suggested.
        """
        
        analysis_result = await Runner.run(agent, analysis_instruction, max_turns=3)
        print(f"   Result: {analysis_result.final_output[:200]}...")
        
        # Test 4: Test applying improvements (with mock suggestions)
        print("\n4. Testing apply improvements...")
        mock_suggestions = json.dumps({
            "analysis": "Test analysis",
            "suggested_modifications": [
                {
                    "location": "__init__ method",
                    "change_type": "modify",
                    "description": "Test modification",
                    "code_snippet": "# Test comment"
                }
            ],
            "expected_impact": "Test impact"
        })
        
        apply_instruction = f"""
        Use apply_query_generator_improvements with this parameter:
        - improvement_suggestions: {mock_suggestions}
        
        Report if the application was successful and how many modifications were applied.
        """
        
        apply_result = await Runner.run(agent, apply_instruction, max_turns=3)
        print(f"   Result: {apply_result.final_output[:200]}...")
        
        print("\n🎉 All tool tests completed!")
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all tests."""
    success = await test_all_tools()
    
    if success:
        print("\n✅ All tools are working correctly!")
    else:
        print("\n❌ Some tools failed. Check the output above for details.")
    
    return 0 if success else 1


if __name__ == "__main__":
    asyncio.run(main())
