2025-06-13T17:01:58.153898Z  INFO embucketd: Listening on http://[::1]:3000
2025-06-13T17:01:59.946182Z DEBUG request{method=GET uri=/health version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:01:59.948135Z DEBUG request{method=GET uri=/health version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=2 ms status=200
2025-06-13T17:01:59.952533Z DEBUG request{method=POST uri=/v1/metastore/volumes version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:01:59.995423Z DEBUG request{method=POST uri=/v1/metastore/volumes version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=42 ms status=200
2025-06-13T17:01:59.999336Z DEBUG request{method=POST uri=/v1/metastore/databases version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:00.093522Z DEBUG request{method=POST uri=/v1/metastore/databases version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=94 ms status=200
2025-06-13T17:02:00.100948Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=ae431f11-2a06-41f5-bc44-52ed3cacce05&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:00.108799Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=ae431f11-2a06-41f5-bc44-52ed3cacce05&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=7 ms status=200
2025-06-13T17:02:00.114790Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=8bba9501-cebd-4e90-8f58-c78b43258c53 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:00.294570Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=8bba9501-cebd-4e90-8f58-c78b43258c53 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=179 ms status=200
2025-06-13T17:02:00.313984Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=1400a09e-baea-42c7-9687-7aabbe3b62e8&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:00.314384Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=1400a09e-baea-42c7-9687-7aabbe3b62e8&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:00.317659Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=cb241c42-1d2e-4de9-b03e-ae805d59a6b5 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:00.494811Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=cb241c42-1d2e-4de9-b03e-ae805d59a6b5 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=177 ms status=200
2025-06-13T17:02:00.500208Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=5119e0fd-e4b2-4d11-ad41-9b15ba40140a&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:00.500681Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=5119e0fd-e4b2-4d11-ad41-9b15ba40140a&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:00.504745Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=5e34664a-b5cf-40e0-adaa-b021af35661c version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:00.693796Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=5e34664a-b5cf-40e0-adaa-b021af35661c version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=189 ms status=200
2025-06-13T17:02:00.698363Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=822f9c89-df8f-4b57-99a1-b363146c7d74&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:00.698730Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=822f9c89-df8f-4b57-99a1-b363146c7d74&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:00.702207Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=0fb9072e-501d-49e0-83cf-1a94d9bbe8aa version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:00.894639Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=0fb9072e-501d-49e0-83cf-1a94d9bbe8aa version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=192 ms status=200
2025-06-13T17:02:00.899947Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=89869c56-505b-424f-be93-adcfd55f4798&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:00.900392Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=89869c56-505b-424f-be93-adcfd55f4798&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:00.904162Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=d2bf2198-2f14-4f7a-813c-958c5ecfb30b version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:01.093586Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=d2bf2198-2f14-4f7a-813c-958c5ecfb30b version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=189 ms status=200
2025-06-13T17:02:01.098646Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=5eafb232-0d12-48f0-9d81-108d859743ed&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:01.099021Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=5eafb232-0d12-48f0-9d81-108d859743ed&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:01.102805Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=9f749b89-3969-450e-9fc6-8afb21099f95 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:01.293178Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=9f749b89-3969-450e-9fc6-8afb21099f95 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:02:01.298524Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=58b4e114-e2e6-4556-927a-ad369ed35ad9&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:01.298968Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=58b4e114-e2e6-4556-927a-ad369ed35ad9&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:01.302715Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=c2b7e7be-4fae-4c38-81d8-5c4962842874 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:01.493608Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=c2b7e7be-4fae-4c38-81d8-5c4962842874 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:02:01.498899Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=2e472a16-ede8-4330-b601-3000cb05a1b4&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:01.499350Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=2e472a16-ede8-4330-b601-3000cb05a1b4&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:01.503208Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=ee1261b5-e2b6-4099-8774-7bf89d62cc0b version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:01.693129Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=ee1261b5-e2b6-4099-8774-7bf89d62cc0b version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=189 ms status=200
2025-06-13T17:02:01.698241Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=9907b2a9-d644-44de-9d5d-5d7f7fc0411a&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:01.698629Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=9907b2a9-d644-44de-9d5d-5d7f7fc0411a&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:01.702674Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=ee898c0d-fd5d-4912-acf8-d4e7dd933665 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:01.893644Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=ee898c0d-fd5d-4912-acf8-d4e7dd933665 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=191 ms status=200
2025-06-13T17:02:01.898888Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=07dad6eb-50b3-449b-88fb-656887ed0de7&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:01.899324Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=07dad6eb-50b3-449b-88fb-656887ed0de7&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:01.903041Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=75548538-e029-4c8f-a2d5-f16d69a4b05b version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:02.094968Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=75548538-e029-4c8f-a2d5-f16d69a4b05b version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=191 ms status=200
2025-06-13T17:02:02.100515Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=da213b64-437d-495b-ab1d-084345bfbb28&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:02.100973Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=da213b64-437d-495b-ab1d-084345bfbb28&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:02.105146Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=0998263a-ea99-4b46-a47d-f3748cad6179 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:02.293838Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=0998263a-ea99-4b46-a47d-f3748cad6179 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=188 ms status=200
2025-06-13T17:02:02.299062Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=56015184-9f30-4b9a-9dab-6b78799367c4&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:02.299530Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=56015184-9f30-4b9a-9dab-6b78799367c4&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:02.303545Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=3c30a026-2982-46f2-8436-2829604204d2 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:02.493943Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=3c30a026-2982-46f2-8436-2829604204d2 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:02:02.499250Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e552b255-e8a1-452d-ad2f-c30b6fad6c3b&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:02.499699Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e552b255-e8a1-452d-ad2f-c30b6fad6c3b&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:02.503484Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=41ae95d7-95c8-4361-8d1e-b23801b4e30f version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:02.693260Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=41ae95d7-95c8-4361-8d1e-b23801b4e30f version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=189 ms status=200
2025-06-13T17:02:02.698450Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=415a3fb6-0866-4317-b6e2-7a50c70cb9d9&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:02.698917Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=415a3fb6-0866-4317-b6e2-7a50c70cb9d9&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:02.702542Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=871ae385-9d56-473f-a618-f0a4c9dece4e version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:02.894573Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=871ae385-9d56-473f-a618-f0a4c9dece4e version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=192 ms status=200
2025-06-13T17:02:02.901986Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=d83a45df-e2b4-48fb-9cb4-05327c9d7c55&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:02.902455Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=d83a45df-e2b4-48fb-9cb4-05327c9d7c55&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:02.906416Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=feba3fec-3748-4e19-86b6-acb806d34845 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:03.093365Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=feba3fec-3748-4e19-86b6-acb806d34845 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=186 ms status=200
2025-06-13T17:02:03.098326Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=9442159d-522b-4265-9c47-5bbf83b6614c&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:03.098731Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=9442159d-522b-4265-9c47-5bbf83b6614c&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:03.102232Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=6518eebb-5406-4ebe-a6e0-ad3d34aa029b version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:03.294540Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=6518eebb-5406-4ebe-a6e0-ad3d34aa029b version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=192 ms status=200
2025-06-13T17:02:03.299560Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=a1f6bcad-c1e3-4dc8-833a-dc0d42dadb2c&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:03.299972Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=a1f6bcad-c1e3-4dc8-833a-dc0d42dadb2c&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:03.304219Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=a1d0d0f4-dfa7-4d3d-a3ac-d433559428c8 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:03.493682Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=a1d0d0f4-dfa7-4d3d-a3ac-d433559428c8 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=189 ms status=200
2025-06-13T17:02:03.498888Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=72742a2e-1f3e-4f3c-9c24-3248842d84ff&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:03.499326Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=72742a2e-1f3e-4f3c-9c24-3248842d84ff&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:03.502866Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=df8f9bb7-b2cb-49b5-a818-704c5b637feb version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:03.795708Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=df8f9bb7-b2cb-49b5-a818-704c5b637feb version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=292 ms status=200
2025-06-13T17:02:03.801065Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=3e7408f5-dc53-43d3-af8b-89bd128970a5&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:03.801527Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=3e7408f5-dc53-43d3-af8b-89bd128970a5&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:03.805545Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=e642a9b1-415d-4dc0-b506-4cb0d4317934 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:03.995019Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=e642a9b1-415d-4dc0-b506-4cb0d4317934 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=189 ms status=200
2025-06-13T17:02:04.000296Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=3b11a868-3c0d-4a35-baaf-754f11abc97d&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:04.000747Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=3b11a868-3c0d-4a35-baaf-754f11abc97d&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:04.004385Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=9af9ab2e-870f-4627-9225-1c7e2a7dea24 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:04.193144Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=9af9ab2e-870f-4627-9225-1c7e2a7dea24 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=188 ms status=200
2025-06-13T17:02:04.197382Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=a1ae5d2c-5178-430d-83c7-eb319bb1787c&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:04.197913Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=a1ae5d2c-5178-430d-83c7-eb319bb1787c&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:04.202779Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=de68b7fd-238f-4e97-af45-3dacde3203e4 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:04.392940Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=de68b7fd-238f-4e97-af45-3dacde3203e4 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:02:04.396533Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=6da1b421-cce8-4675-9933-a69c35bbfd61&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:04.396923Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=6da1b421-cce8-4675-9933-a69c35bbfd61&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:04.400297Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=07df26c9-76a9-4989-866c-91e74ecdb8ae version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:04.594547Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=07df26c9-76a9-4989-866c-91e74ecdb8ae version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=194 ms status=200
2025-06-13T17:02:04.599818Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=b50e5248-7d7c-4e98-9dfd-1ee806b07662&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:04.600235Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=b50e5248-7d7c-4e98-9dfd-1ee806b07662&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:04.603849Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=322bff2c-e6ab-42f1-9fba-11a20a0787ab version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:04.794832Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=322bff2c-e6ab-42f1-9fba-11a20a0787ab version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:02:04.825133Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=0d71819d-7bc4-4099-af32-72c6f310162c&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:04.825498Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=0d71819d-7bc4-4099-af32-72c6f310162c&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:04.829045Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=a204d020-d720-481e-8e57-8e459bbda9c7 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:04.993303Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=a204d020-d720-481e-8e57-8e459bbda9c7 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=164 ms status=200
2025-06-13T17:02:05.108617Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=d97fb284-1c7e-4cbc-9ac3-671a3c8eefe6&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:05.108981Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=d97fb284-1c7e-4cbc-9ac3-671a3c8eefe6&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:05.112289Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=72a2cad7-8f99-4ad7-917e-3486bd80c3db version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:05.193398Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=72a2cad7-8f99-4ad7-917e-3486bd80c3db version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=81 ms status=200
2025-06-13T17:02:05.307095Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=dbb5dc91-e52b-4bdc-8ea4-48ad4dd69922&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:05.307511Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=dbb5dc91-e52b-4bdc-8ea4-48ad4dd69922&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:05.311311Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=5a3be4f3-8cca-4653-9bb5-a51404d072d5 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:05.393406Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=5a3be4f3-8cca-4653-9bb5-a51404d072d5 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=82 ms status=200
2025-06-13T17:02:05.510663Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=4ec7af61-a8ed-42d4-9e72-e5ab7b71fb6d&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:05.511087Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=4ec7af61-a8ed-42d4-9e72-e5ab7b71fb6d&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:05.515073Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=95a45383-46ba-46e7-a812-d6bd6a4af43f version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:05.593612Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=95a45383-46ba-46e7-a812-d6bd6a4af43f version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=78 ms status=200
2025-06-13T17:02:05.707911Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=4b809213-0111-4e5c-9983-f26026844209&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:05.708324Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=4b809213-0111-4e5c-9983-f26026844209&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:05.711956Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=43ae684a-27c1-4fee-adcf-5877c6b5d84a version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:05.792942Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=43ae684a-27c1-4fee-adcf-5877c6b5d84a version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=81 ms status=200
2025-06-13T17:02:05.904268Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=85a9d5ce-72c4-4f6e-bf3b-e02a1f27bd84&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:05.904636Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=85a9d5ce-72c4-4f6e-bf3b-e02a1f27bd84&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:05.908031Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=5ddb357b-6be4-41b1-b3de-81ad265d31ac version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:05.993426Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=5ddb357b-6be4-41b1-b3de-81ad265d31ac version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=85 ms status=200
2025-06-13T17:02:06.109326Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=5e1cf66e-5f68-4903-98fc-17e13ab5b123&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:06.109777Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=5e1cf66e-5f68-4903-98fc-17e13ab5b123&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:06.113761Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=cf3237f1-b07c-4923-aef2-2566ebd27e30 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:06.193001Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=cf3237f1-b07c-4923-aef2-2566ebd27e30 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=79 ms status=200
2025-06-13T17:02:06.307614Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=14094689-62bb-46ce-b8bc-7a581653439b&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:06.308035Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=14094689-62bb-46ce-b8bc-7a581653439b&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:06.311864Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=f5292d79-c67a-457c-aff8-e82f799c4501 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:06.393218Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=f5292d79-c67a-457c-aff8-e82f799c4501 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=81 ms status=200
2025-06-13T17:02:06.505614Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=161e9e86-70d9-488c-b82b-28d7f67a8882&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:06.506022Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=161e9e86-70d9-488c-b82b-28d7f67a8882&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:06.509437Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=48114793-29a3-410a-9ebc-3fc27193bd38 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:06.593051Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=48114793-29a3-410a-9ebc-3fc27193bd38 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=83 ms status=200
2025-06-13T17:02:06.702404Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=5230b9be-4ae9-4feb-9800-7b1b9902564c&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:06.702796Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=5230b9be-4ae9-4feb-9800-7b1b9902564c&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:06.707167Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=bf4c3fb9-8656-4688-ba77-210e6b21629e version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:06.794250Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=bf4c3fb9-8656-4688-ba77-210e6b21629e version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=87 ms status=200
2025-06-13T17:02:06.910324Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=9060c7a0-6a3c-4750-ace9-c5632ff1f80c&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:06.910731Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=9060c7a0-6a3c-4750-ace9-c5632ff1f80c&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:06.914456Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=384c4250-130b-4eef-b171-a95bc11cf679 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:06.993578Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=384c4250-130b-4eef-b171-a95bc11cf679 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=79 ms status=200
2025-06-13T17:02:07.104836Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=6d0a2ca2-871a-48a1-9670-ac7e2c880f06&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:07.105184Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=6d0a2ca2-871a-48a1-9670-ac7e2c880f06&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:07.108246Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=d4f28013-943a-4bb6-ae2b-a410445456c5 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:07.193101Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=d4f28013-943a-4bb6-ae2b-a410445456c5 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=84 ms status=200
2025-06-13T17:02:07.304861Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=f0fa6710-0b62-4232-b035-7c42363a6be8&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:07.305259Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=f0fa6710-0b62-4232-b035-7c42363a6be8&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:07.308777Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=920d42a3-f747-4cb3-b6f9-74e795172725 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:07.393207Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=920d42a3-f747-4cb3-b6f9-74e795172725 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=84 ms status=200
2025-06-13T17:02:07.503945Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=2d2974dc-6e4c-4d96-8747-0273842fcec0&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:07.504323Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=2d2974dc-6e4c-4d96-8747-0273842fcec0&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:07.507971Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=0b8d3144-8632-4d2d-865a-ddef48f4bbfe version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:07.593578Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=0b8d3144-8632-4d2d-865a-ddef48f4bbfe version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=85 ms status=200
2025-06-13T17:02:07.710670Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=ffedc2c2-1cc6-4c98-9d5d-e9e27e76c7f3&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:07.711108Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=ffedc2c2-1cc6-4c98-9d5d-e9e27e76c7f3&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:07.715123Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=ea55fb2a-bb2a-4e82-8b5c-b3eb3832d81e version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:07.793756Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=ea55fb2a-bb2a-4e82-8b5c-b3eb3832d81e version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=78 ms status=200
2025-06-13T17:02:07.908898Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=a28b15e1-8881-4669-9d32-efc5080f0d57&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:07.909471Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=a28b15e1-8881-4669-9d32-efc5080f0d57&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:07.913769Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=cb9d6c20-d6eb-4dd2-8d30-e341cdb0d2b7 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:07.993133Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=cb9d6c20-d6eb-4dd2-8d30-e341cdb0d2b7 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=79 ms status=200
2025-06-13T17:02:08.106376Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=c5e2f9f2-6a50-41ed-ab2f-3405a43dbb89&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:08.106780Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=c5e2f9f2-6a50-41ed-ab2f-3405a43dbb89&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:08.110454Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=5ec9a24a-8306-432f-9d64-926783e1d672 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:08.193779Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=5ec9a24a-8306-432f-9d64-926783e1d672 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=83 ms status=200
2025-06-13T17:02:08.303985Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=591c12bf-2adc-4c2c-a31f-88d3eb760228&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:08.304402Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=591c12bf-2adc-4c2c-a31f-88d3eb760228&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:08.308059Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=fbc762e0-e318-4a81-844c-c7dbb7eaa6fc version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:08.493467Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=fbc762e0-e318-4a81-844c-c7dbb7eaa6fc version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=185 ms status=200
2025-06-13T17:02:08.608444Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=411747ff-decc-4f73-b308-0b3b88344fa2&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:08.608850Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=411747ff-decc-4f73-b308-0b3b88344fa2&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:08.612358Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=e387d4f8-dcee-4ca7-aba1-9cfffc66da83 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:08.693776Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=e387d4f8-dcee-4ca7-aba1-9cfffc66da83 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=81 ms status=200
2025-06-13T17:02:08.803677Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=ee8c649b-3059-4974-accd-b27227cda457&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:08.804081Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=ee8c649b-3059-4974-accd-b27227cda457&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:08.807455Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=c1f86da5-3963-4509-8f05-9c6e0013ff91 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:08.894489Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=c1f86da5-3963-4509-8f05-9c6e0013ff91 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=87 ms status=200
2025-06-13T17:02:09.006548Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=69ed8ccd-1455-4cf9-b728-11befa1f4fcb&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:09.006960Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=69ed8ccd-1455-4cf9-b728-11befa1f4fcb&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:09.010701Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=9bc133c6-b7cc-4bda-bd29-c65523686079 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:09.094028Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=9bc133c6-b7cc-4bda-bd29-c65523686079 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=83 ms status=200
2025-06-13T17:02:09.205490Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=8618679a-551d-4ac8-be3c-a696e5f2910a&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:09.205901Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=8618679a-551d-4ac8-be3c-a696e5f2910a&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:09.209435Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=2cfd9c5e-2ace-470b-8dee-20a060191ddf version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:09.293597Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=2cfd9c5e-2ace-470b-8dee-20a060191ddf version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=84 ms status=200
2025-06-13T17:02:09.407467Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=c570754f-94e0-44f9-8c2b-5b7a48a0fb91&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:09.407868Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=c570754f-94e0-44f9-8c2b-5b7a48a0fb91&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:09.411344Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=6bdbd5b0-de89-4d72-b985-64ecf42c4afb version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:09.493340Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=6bdbd5b0-de89-4d72-b985-64ecf42c4afb version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=82 ms status=200
2025-06-13T17:02:09.608408Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=6c340d72-4575-4aa6-b005-adf9d7164492&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:09.608818Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=6c340d72-4575-4aa6-b005-adf9d7164492&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:09.611921Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=4989cf72-0678-46a4-a5e5-507b790c3a7a version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:09.693704Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=4989cf72-0678-46a4-a5e5-507b790c3a7a version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=81 ms status=200
2025-06-13T17:02:09.807194Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=4090b9c9-ccb6-46e1-b6a8-f6764813786a&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:09.807628Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=4090b9c9-ccb6-46e1-b6a8-f6764813786a&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:02:09.812097Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=7d8de7c1-7396-406a-8787-6108915b1149 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:02:09.893310Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=7d8de7c1-7396-406a-8787-6108915b1149 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=81 ms status=200
2025-06-13T17:02:09.927555Z  WARN embucketd: Ctrl+C received, starting graceful shutdown
2025-06-13T17:02:09.927875Z  WARN embucketd: signal received, starting graceful shutdown
