2025-06-13T17:12:15.981984Z  INFO embucketd: Listening on http://[::1]:3000
2025-06-13T17:12:17.736481Z DEBUG request{method=GET uri=/health version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:17.739696Z DEBUG request{method=GET uri=/health version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=4 ms status=200
2025-06-13T17:12:17.746773Z DEBUG request{method=POST uri=/v1/metastore/volumes version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:17.798380Z DEBUG request{method=POST uri=/v1/metastore/volumes version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=51 ms status=200
2025-06-13T17:12:17.802081Z DEBUG request{method=POST uri=/v1/metastore/databases version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:17.895221Z DEBUG request{method=POST uri=/v1/metastore/databases version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=93 ms status=200
2025-06-13T17:12:17.901916Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=4ceff5cd-e551-44f0-8fdf-f0ea14cf6e35&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:17.912504Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=4ceff5cd-e551-44f0-8fdf-f0ea14cf6e35&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=10 ms status=200
2025-06-13T17:12:17.917923Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=0f4dcc44-48d7-45e3-87a9-348439aa174d version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:18.199374Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=0f4dcc44-48d7-45e3-87a9-348439aa174d version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=281 ms status=200
2025-06-13T17:12:18.219096Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=22554619-15ff-43c2-b2ca-d9273688c762&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:18.219500Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=22554619-15ff-43c2-b2ca-d9273688c762&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:12:18.223278Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=710723dd-af89-40cf-80d0-04fb42261c71 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:18.395069Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=710723dd-af89-40cf-80d0-04fb42261c71 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=171 ms status=200
2025-06-13T17:12:18.400369Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=63e8c03d-75b4-463c-9ac8-c7df48ef7892&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:18.400774Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=63e8c03d-75b4-463c-9ac8-c7df48ef7892&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:12:18.404187Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=d162ddeb-68a3-411b-8d88-5ad202cb89cb version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:18.593987Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=d162ddeb-68a3-411b-8d88-5ad202cb89cb version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=189 ms status=200
2025-06-13T17:12:18.598265Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e911384f-6cee-433d-80c9-72da76f2e2b0&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:18.598725Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=e911384f-6cee-433d-80c9-72da76f2e2b0&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:12:18.602820Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=7fa70a43-606a-46b3-a4ce-3699f6a6cbcd version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:18.794938Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=7fa70a43-606a-46b3-a4ce-3699f6a6cbcd version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=192 ms status=200
2025-06-13T17:12:18.799945Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=f32ce398-276d-47bf-981c-b1c39ea91340&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:18.800382Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=f32ce398-276d-47bf-981c-b1c39ea91340&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:12:18.804451Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=c5e71ebb-1594-4224-a863-8d990adff74d version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:18.995214Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=c5e71ebb-1594-4224-a863-8d990adff74d version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:12:19.000303Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=1f92e0b6-5bfc-4ed9-bdae-00c6ef25f011&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:19.000694Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=1f92e0b6-5bfc-4ed9-bdae-00c6ef25f011&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:12:19.005609Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=47015438-1be5-499d-be85-4245ed23e415 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:19.194633Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=47015438-1be5-499d-be85-4245ed23e415 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=189 ms status=200
2025-06-13T17:12:19.199828Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=96107c64-d838-4c95-9486-18951a45bca5&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:19.200269Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=96107c64-d838-4c95-9486-18951a45bca5&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:12:19.203761Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=400a0969-e65f-48c3-be95-5b6e50d36c8c version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:19.394340Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=400a0969-e65f-48c3-be95-5b6e50d36c8c version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:12:19.398564Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=4baa8207-92dd-4a01-a32f-98f8d581232f&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:19.398979Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=4baa8207-92dd-4a01-a32f-98f8d581232f&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:12:19.402546Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=c1796d4f-8f5a-4dc4-aae8-dc4d4a451146 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:19.594194Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=c1796d4f-8f5a-4dc4-aae8-dc4d4a451146 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=191 ms status=200
2025-06-13T17:12:19.597973Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=8d36ef02-b999-419f-8422-e5f6c622e4d9&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:19.598340Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=8d36ef02-b999-419f-8422-e5f6c622e4d9&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:12:19.601543Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=635b437f-efaf-43d9-9a4e-1cfd685676d7 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:19.795418Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=635b437f-efaf-43d9-9a4e-1cfd685676d7 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=193 ms status=200
2025-06-13T17:12:19.800582Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=b0f56a18-634b-4da4-a24d-c9b23cb12f81&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:19.801021Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=b0f56a18-634b-4da4-a24d-c9b23cb12f81&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:12:19.804923Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=c48cd91b-ef3f-42b1-8baa-93de0b05beb0 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:19.995486Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=c48cd91b-ef3f-42b1-8baa-93de0b05beb0 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:12:20.000342Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=d59d03d5-f0ba-43e1-b082-ebdd1cf95330&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:20.000775Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=d59d03d5-f0ba-43e1-b082-ebdd1cf95330&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:12:20.004337Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=ff2b96cf-69e9-4d85-8449-96b117613505 version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:20.194841Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=ff2b96cf-69e9-4d85-8449-96b117613505 version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=190 ms status=200
2025-06-13T17:12:20.200128Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=517a08c5-a9b1-442e-9795-f6b446ca673f&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:20.200586Z DEBUG request{method=POST uri=/session/v1/login-request?request_id=517a08c5-a9b1-442e-9795-f6b446ca673f&databaseName=embucket&schemaName=public&warehouse=COMPUTE_WH version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:12:20.204232Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=48dedbae-264c-4ca9-ae5b-9a253c54ee3c version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:20.388770Z DEBUG request{method=POST uri=/queries/v1/abort-request?requestId=62b8fd54-d0b6-4834-9149-8d6afeb2385c version=HTTP/1.1}: tower_http::trace::on_request: started processing request
2025-06-13T17:12:20.388908Z DEBUG request{method=POST uri=/queries/v1/abort-request?requestId=62b8fd54-d0b6-4834-9149-8d6afeb2385c version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=0 ms status=200
2025-06-13T17:12:20.389047Z  WARN embucketd: Ctrl+C received, starting graceful shutdown
2025-06-13T17:12:20.389051Z DEBUG request{method=POST uri=/queries/v1/query-request?requestId=48dedbae-264c-4ca9-ae5b-9a253c54ee3c version=HTTP/1.1}: tower_http::trace::on_response: finished processing request latency=184 ms status=200
2025-06-13T17:12:20.389074Z  WARN embucketd: signal received, starting graceful shutdown
