#!/usr/bin/env python3
"""
Test script to verify the iterative improvement agent can start correctly.
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Load environment variables
load_dotenv()

async def test_agent_startup():
    """Test that the agent can be created and initialized."""
    try:
        print("Testing agent startup...")
        
        # Test imports
        print("1. Testing imports...")
        from agents import Agent, Runner
        from tools.comprehensive_fuzzing_tool import run_comprehensive_fuzzing_session
        from tools.query_generator_improvement_tool import analyze_and_improve_query_generator
        from tools.code_modification_tool import (
            apply_query_generator_improvements, 
            backup_and_restore_query_generator,
            read_query_generator_code
        )
        print("✅ All imports successful")
        
        # Test file detection
        print("2. Testing file detection...")
        from tools.code_modification_tool import _find_query_generator_file
        file_path = _find_query_generator_file()
        if file_path and os.path.exists(file_path):
            print(f"✅ Query generator file found: {file_path}")
        else:
            print("❌ Query generator file not found")
            return False
        
        # Test agent creation (without OpenAI key requirement)
        print("3. Testing agent creation...")
        
        # Temporarily set a dummy API key for testing
        original_key = os.getenv("OPENAI_API_KEY")
        os.environ["OPENAI_API_KEY"] = "test-key-for-startup-test"
        
        try:
            from iterative_improvement_agent import IterativeImprovementAgent
            
            # Create agent instance
            agent = IterativeImprovementAgent()
            print("✅ Agent created successfully")
            print(f"   Max iterations: {agent.max_iterations}")
            print(f"   Queries per iteration: {agent.queries_per_iteration}")
            print(f"   Embucket URL: {agent.embucket_url}")
            print(f"   Output directory: {agent.slt_output_dir}")
            
            return True
            
        finally:
            # Restore original API key
            if original_key:
                os.environ["OPENAI_API_KEY"] = original_key
            else:
                os.environ.pop("OPENAI_API_KEY", None)
        
    except Exception as e:
        print(f"❌ Error during agent startup test: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("=" * 60)
    print("ITERATIVE IMPROVEMENT AGENT STARTUP TEST")
    print("=" * 60)
    
    success = await test_agent_startup()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ AGENT STARTUP TEST PASSED")
        print("The iterative improvement agent can be created successfully.")
        print("To run the full agent, use: ./run_iterative_improvement.sh")
        return 0
    else:
        print("❌ AGENT STARTUP TEST FAILED")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
