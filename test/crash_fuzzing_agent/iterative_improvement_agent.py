#!/usr/bin/env python3
"""
Iterative Improvement Agent for Embucket Fuzzing

This agent runs fuzzing sessions in a loop, analyzing results and improving
the query generator when no bugs are found, until either a crash is detected
or 10 iterations are completed.
"""

import os
import time
import json
from dotenv import load_dotenv

from agents import Agent, Runner

# Load environment variables from .env file
load_dotenv()

# Import the comprehensive fuzzing tool
from tools.comprehensive_fuzzing_tool import run_comprehensive_fuzzing_session

# Import improvement tools
from tools.query_generator_improvement_tool import analyze_and_improve_query_generator
from tools.code_modification_tool import apply_query_generator_improvements, backup_and_restore_query_generator, read_query_generator_code


class IterativeImprovementAgent(Agent):
    """AI agent that iteratively improves query generation to find bugs."""

    def __init__(self):
        # Load configuration from environment variables
        self.embucket_host = os.getenv("EMBUCKET_HOST", "localhost")
        self.embucket_port = int(os.getenv("EMBUCKET_PORT", "3000"))
        self.embucket_url = f"http://{self.embucket_host}:{self.embucket_port}"
        self.slt_output_dir = os.getenv("SLT_OUTPUT_DIR", "test/sql/fuzz_regressions")
        self.max_iterations = 10
        self.queries_per_iteration = int(os.getenv("DEFAULT_NUM_QUERIES", "5"))

        # Verify OpenAI API key is set
        openai_api_key = os.getenv("OPENAI_API_KEY")
        if not openai_api_key:
            raise ValueError(
                "OPENAI_API_KEY environment variable is required. "
                "Please set it in the .env file or as an environment variable."
            )

        super().__init__(
            name="IterativeImprovementAgent",
            instructions="""You are an iterative fuzzing improvement agent. Your goal is to find bugs by running fuzzing sessions and improving the query generator when no bugs are found.

Your workflow:
1. Run a fuzzing session using run_comprehensive_fuzzing_session
2. Analyze the results to check if any actual bugs were found
3. If bugs were found, report success and stop
4. If no bugs were found, use analyze_and_improve_query_generator to get improvement suggestions
5. Apply the improvements using apply_query_generator_improvements
6. Repeat until bugs are found or max iterations reached

Always provide detailed analysis of each iteration's results and explain what improvements were made.""",
            tools=[
                run_comprehensive_fuzzing_session,
                analyze_and_improve_query_generator,
                apply_query_generator_improvements,
                backup_and_restore_query_generator,
                read_query_generator_code
            ]
        )

        # Ensure output directory exists
        os.makedirs(self.slt_output_dir, exist_ok=True)

    async def run_iterative_improvement(self):
        """
        Run the iterative improvement process.
        """
        print(f"🚀 Starting iterative improvement process...")
        print(f"📊 Configuration:")
        print(f"   Max iterations: {self.max_iterations}")
        print(f"   Queries per iteration: {self.queries_per_iteration}")
        print(f"   Embucket: {self.embucket_url}")
        print(f"   Output directory: {self.slt_output_dir}")
        print()

        # Create initial backup of query generator
        backup_instruction = """
        Create a backup of the current query generator file before starting the iterative improvement process.
        Use backup_and_restore_query_generator with action="backup".
        """
        
        backup_result = await Runner.run(self, backup_instruction, max_turns=2)
        print(f"Backup result: {backup_result.final_output}")

        # Main iterative improvement instruction
        improvement_instruction = f"""
        Run an iterative improvement process to find bugs in Embucket. Follow this exact workflow:

        FOR EACH ITERATION (1 to {self.max_iterations}):
        
        1. **Run Fuzzing Session**: 
           Use run_comprehensive_fuzzing_session with:
           - num_queries: {self.queries_per_iteration}
           - complexity: "complex"
           - host: "{self.embucket_host}"
           - port: {self.embucket_port}
           - output_dir: "{self.slt_output_dir}"

        2. **Analyze Results**:
           Check the fuzzing results for actual_bugs > 0 or crashes_found > 0
           
        3. **If bugs found**: 
           - Report SUCCESS and STOP the process
           - Provide detailed analysis of the bugs found
           
        4. **If no bugs found**:
           - Use read_query_generator_code to get the current query generator code
           - Use analyze_and_improve_query_generator to get improvement suggestions
           - Use apply_query_generator_improvements to apply the suggestions
           - Continue to next iteration

        5. **Track Progress**:
           - Report iteration number, success rates, and improvements made
           - Explain what changes were applied and why

        STOP CONDITIONS:
        - Any actual bugs found (crashes, server errors, timeouts)
        - Maximum iterations ({self.max_iterations}) reached
        - Critical error preventing continuation

        Provide a comprehensive final report including:
        - Total iterations completed
        - Whether bugs were found
        - Summary of all improvements made
        - Recommendations for next steps
        """

        # Run the iterative improvement process
        result = await Runner.run(self, improvement_instruction, max_turns=50)
        
        print(f"\n🏁 Iterative improvement process completed!")
        print(f"Final result: {result.final_output}")
        
        return result


async def main():
    """Main entry point for the iterative improvement agent."""
    try:
        agent = IterativeImprovementAgent()
        
        print("=" * 80)
        print("EMBUCKET ITERATIVE IMPROVEMENT FUZZING AGENT")
        print("=" * 80)
        print()
        print("This agent will:")
        print("1. Run fuzzing sessions to test Embucket")
        print("2. Analyze results and improve query generation when no bugs found")
        print("3. Loop until bugs are found or 10 iterations completed")
        print()
        
        await agent.run_iterative_improvement()

    except ValueError as e:
        print(f"Configuration Error: {e}")
        print("\nPlease check your .env file and ensure OPENAI_API_KEY is set.")
        return 1
    except Exception as e:
        print(f"Error: {e}")
        return 1

    return 0


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
