# Iterative Improvement Fuzzing Agent

This agent automatically improves the SQL query generator to find bugs in Embucket by running fuzzing sessions in a loop and making the queries more complex when no bugs are found.

## Overview

The Iterative Improvement Agent works by:

1. **Running fuzzing sessions** using the existing comprehensive fuzzing tool
2. **Analyzing results** to check if any actual bugs (crashes, server errors, timeouts) were found
3. **If no bugs found**, analyzing the query generation patterns and suggesting improvements
4. **Applying improvements** to make queries more complex and aggressive
5. **Repeating** until either a crash is found or 10 iterations are completed

## Key Features

- **Automatic query complexity escalation**: Each iteration makes queries more aggressive
- **Intelligent analysis**: Uses LLM-style analysis to identify why queries aren't finding bugs
- **Safe modifications**: Creates backups before modifying code
- **Early termination**: Stops immediately when bugs are found
- **Comprehensive logging**: Tracks all changes and their impact

## Files

### Core Agent
- `iterative_improvement_agent.py` - Main agent that orchestrates the improvement process

### Tools
- `tools/query_generator_improvement_tool.py` - Analyzes fuzzing results and suggests improvements
- `tools/code_modification_tool.py` - Applies improvements to the query generator code

### Testing
- `test_iterative_improvement.py` - Test script to verify the agent works correctly

## Configuration

Set these environment variables in your `.env` file:

```bash
# Required
OPENAI_API_KEY=your_openai_api_key_here

# Optional (with defaults)
EMBUCKET_HOST=localhost
EMBUCKET_PORT=3000
SLT_OUTPUT_DIR=test/sql/fuzz_regressions
DEFAULT_NUM_QUERIES=5
DEFAULT_SAFE_QUERY_PROBABILITY=0.3
```

## Usage

### Basic Usage

```bash
cd test/crash_fuzzing_agent
python iterative_improvement_agent.py
```

### Testing First

```bash
cd test/crash_fuzzing_agent
python test_iterative_improvement.py
```

## How It Works

### Iteration Process

Each iteration follows this workflow:

1. **Fuzzing Session**: Runs 5 queries (configurable) against Embucket
2. **Result Analysis**: Checks for actual bugs vs expected errors
3. **Success Check**: If bugs found, reports success and stops
4. **Improvement**: If no bugs, analyzes patterns and improves query generator
5. **Code Update**: Applies improvements to make queries more complex

### Improvement Strategy

The agent progressively makes queries more aggressive:

- **Iteration 1-2**: Reduces safe query probability
- **Iteration 3-4**: Adds window functions and edge cases
- **Iteration 5-6**: Adds recursive CTEs and division by zero tests
- **Iteration 7+**: Always uses most aggressive query generation

### Example Improvements

- **Reduce success rate**: Lower `safe_query_probability` from 0.4 to 0.1
- **Add window functions**: `ROW_NUMBER()`, `RANK()`, `LAG()`, `LEAD()`
- **Edge case conditions**: Division by zero, overflow conditions
- **Recursive CTEs**: Complex recursive queries that might cause stack overflow
- **Complex joins**: Multiple table joins with complex conditions

## Output

The agent provides detailed output including:

- **Iteration progress**: Current iteration and results
- **Success rates**: Percentage of successful vs failed queries
- **Improvements applied**: What changes were made to the query generator
- **Bug detection**: Details of any bugs found
- **Final report**: Summary of all iterations and recommendations

## Example Output

```
🚀 Starting iterative improvement process...
📊 Configuration:
   Max iterations: 10
   Queries per iteration: 5
   Embucket: http://localhost:3000
   Output directory: test/sql/fuzz_regressions

=== ITERATION 1 ===
🎯 Generating and executing 5 queries...
✅ Success rate: 80% (too high for aggressive fuzzing)
🔧 Applying improvements: Reducing safe_query_probability to 0.35

=== ITERATION 2 ===
🎯 Generating and executing 5 queries...
✅ Success rate: 60% (still too high)
🔧 Applying improvements: Adding window functions

=== ITERATION 3 ===
🎯 Generating and executing 5 queries...
🚨 CRASH DETECTED: Server connection lost on query 3
💾 Saved SLT file: crash_1234567890_3.slt
🏁 SUCCESS: Bug found in iteration 3!
```

## Troubleshooting

### Common Issues

1. **"OPENAI_API_KEY not set"**
   - Add your OpenAI API key to the `.env` file

2. **"Target file not found"**
   - Ensure you're running from the `test/crash_fuzzing_agent` directory
   - Check that `tools/random_query_generator_tool.py` exists

3. **"Server build failed"**
   - Ensure Embucket can be built with `cargo build --bin embucketd`
   - Check that you're in the correct workspace directory

4. **Agent gets stuck**
   - The agent has a maximum of 50 turns and 10 iterations
   - Check the logs for any error messages

### Testing the Setup

Run the test script to verify everything is working:

```bash
python test_iterative_improvement.py
```

This will test:
- Agent creation
- Tool imports
- Query generator reading
- Backup functionality

## Advanced Usage

### Custom Configuration

You can modify the agent behavior by editing the configuration:

```python
# In iterative_improvement_agent.py
self.max_iterations = 15  # Run more iterations
self.queries_per_iteration = 10  # Test more queries per iteration (or change DEFAULT_NUM_QUERIES in .env)
```

### Manual Improvements

You can also manually add improvements to the query generator by editing:
- `tools/query_generator_improvement_tool.py` - Add new improvement suggestions
- `tools/code_modification_tool.py` - Add new code modification patterns

## Integration

This agent integrates with the existing fuzzing infrastructure:

- Uses `tools/comprehensive_fuzzing_tool.py` for actual fuzzing
- Modifies `tools/random_query_generator_tool.py` to improve queries
- Saves results to the same SLT directory as other fuzzing tools
- Creates backups before making any changes

## Next Steps

After running the iterative improvement agent:

1. **Review any bugs found** in the SLT files
2. **Analyze the improvements made** to understand what patterns find bugs
3. **Run additional targeted tests** based on the findings
4. **Consider making the successful improvements permanent** in the query generator
